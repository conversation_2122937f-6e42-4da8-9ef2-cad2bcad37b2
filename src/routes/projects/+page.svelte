<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { browser } from '$app/environment';

	let canvasElement: HTMLCanvasElement;
	let scene: any = null;
	let scrollContainer: HTMLElement;

	const projects = [
		{
			id: 1,
			title: 'Virtual Veterinary: Meta Bones',
			description: 'An interactive educational tool built with Unity 3D to explore the canine skeletal system. ' +
				'I led a 3-person team, automated 3D model processing with Python, and developed C# mechanics for navigation, ' +
				'animations, and exploration. All optimized for web.',
			technologies: ['Unity 3D', 'C#', 'JavaScript', 'Blender', 'Python'],
			image: 'images/MetaBonesScreenshot.jpg',
			demoUrl: 'https://marioc9955.github.io/VeterinariaVirtual/',
			featured: true
		},
		{
			id: 2,
			title: '3D Portfolio Website',
			description: 'An immersive portfolio website showcasing Three.js capabilities with interactive 3D elements, ' +
				'particle systems, and scroll-based animations.',
			technologies: ['Three.js', 'Svelte', 'WebGL', 'GLS<PERSON>', 'Vite', 'Netlify'],
			image: 'images/3DPortfolioScreenshot.jpg',
			demoUrl: 'https://mario-cabrera-portfolio.netlify.app/',
			featured: true
		},
		{
			id: 3,
			title: 'Chaos Ball',
			description: 'A physics-based 3D game built with Unity, featuring smart cannon mechanics powered by machine learning. ' +
				'I designed the terrain in Blender, developed gameplay in C#, and implemented object destruction and ragdoll physics. ' +
				'Using Unity ML Agents, I trained AI to aim and shoot using reinforcement and imitation learning.',
			technologies: ['Unity 3D', 'C#', 'Unity Machine Learning Agents', 'Blender'],
			image: 'images/ChaosBallScreenshot.jpg',
			demoUrl: 'https://marioc9955.itch.io/chaos-ball',
			githubUrl: 'https://github.com/Marioc9955/Training3DCannon',
			featured: true
		},
		{
			id: 4,
			title: 'Rancho D\' Ortiz – Ranch Business Website',
			description: 'A clean, multi page business site built with Next.js for a ranch. Includes home, about, mission, products, ' +
				'and services pages. The products and services are displayed with Swiper sliders for a smooth, responsive browsing experience.',
			technologies: ['React', 'Node.js', 'Next.js', 'TypeScript', 'Vercel'],
			image: 'images/RanchScreenshot.jpg',
			demoUrl: 'https://www.ranchodortiz.com/',
			featured: false
		},
		{
			id: 5,
			title: 'BB Nails and Spa Salon – Appointment Driven Website',
			description: 'A sleek, service focused website for a nail and spa salon, featuring a homepage, service listings, ' +
				'and a contact section. The appointments page integrates with the client’s Square Appointment system, ' +
				'making it easy for customers to book online, and for the client to edit her services.',
			technologies: ['Svelte', 'Node.js', 'TypeScript', 'Netlify'],
			image: 'images/BBScreenshot.jpg',
			demoUrl: 'https://bbnailsandspasalon.com/',
			featured: false
		},
		{
			id: 6,
			title: 'Guangopolo: El Primer Cedazo – Legend Inspired 2D Game',
			description: 'A culturally themed 2D game developed in Unity to bring a local legend to life. ' +
				'I led a 4-person team using Scrum and designed multiple levels with unique gameplay mechanics, all implemented in C#.',
			technologies: ['Unity 3D', 'C#', 'JavaScript'],
			image: 'images/GuangopoloScreenshot.jpg',
			demoUrl: 'https://marioc9955.itch.io/guangopolo-el-primer-cedazo',
			featured: false
		},
		{
			id: 7,
			title: 'Paradise Shrooms – 2D Adventure Game',
			description: 'A handcrafted 2D game featuring intro cinematics, NPC-guided tutorials, ' +
				'and smooth melee and ranged combat mechanics. Built in Unity with C#, it also ' +
				'includes dynamic cape physics for the hero, adding a subtle touch of realism to the gameplay.',
			technologies: ['Unity 3D', 'C#'],
			image: 'images/ParadiseShroomsScrenshoot.png',
			demoUrl: 'https://marioc9955.itch.io/paradise-shrooms',

		},
		{
			id: 8,
			title: 'Project Alkawing – Endless Procedural Space Survival',
			description: 'A fast-paced 2D space game built in Unity, where players pilot a spaceship through an endless, ' +
				'procedurally generated universe. Collect energy cells to stay alive, dodge or destroy asteroids, ' +
				'and survive as long as you can in deep space.',
			technologies: ['Unity 3D', 'C#'],
			image: 'images/ProjectAlkawingScrenshoot.png',
			demoUrl: 'https://marioc9955.itch.io/project-alkawing',
		},
		{
			id: 9,
			title: 'Project Arwing FR – 3D Procedural World in Java & OpenGL',
			description: 'An academic project focused on real-time 3D navigation through a procedurally generated space environment. ' +
				'Developed using Java and OpenGL, featuring a fully controllable spaceship and dynamic world generation techniques.',
			technologies: ['Java', 'OpenGL'],
			image: 'images/ProjectArwingScreenshot.jpg',
			demoUrl: 'https://www.youtube.com/watch?v=hVJ9-Ra71UE',
			githubUrl: 'https://github.com/Marioc9955/ProjectArwingFR',
		}
	];

	onMount(async () => {
		if (browser && canvasElement) {
			const { ParticleScene } = await import('$lib/three/scenes/ParticleScene');
			scene = new ParticleScene(canvasElement);
		}
	});

	onDestroy(() => {
		if (scene) {
			scene.destroy();
		}
	});

	const scrollLeft = () => {
		scrollContainer.scrollBy({ left: -400, behavior: 'smooth' });
	};

	const scrollRight = () => {
		scrollContainer.scrollBy({ left: 400, behavior: 'smooth' });
	};
</script>

<svelte:head>
	<title>Projects - My Portfolio</title>
	<meta name="description" content="Explore my latest projects and development work showcasing modern web technologies and creative solutions." />
</svelte:head>

<!-- 3D Background Canvas -->
<canvas
	bind:this={canvasElement}
	class="fixed top-0 left-0 w-full h-full -z-10"
	style="pointer-events: none;"
></canvas>

<!-- Projects Section -->
<section class="relative min-h-screen pt-24 pb-20 px-4 sm:px-6 lg:px-8">
	<div class="max-w-7xl mx-auto">
		<!-- Header -->
		<div class="text-center text-white mb-16">
			<div class="bg-gray-700/40 backdrop-blur-sm rounded-lg p-8 mb-8">
				<h1 class="text-4xl md:text-6xl font-bold mb-6">
					<span class="bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-400 bg-clip-text text-transparent">
						My Projects
					</span>
				</h1>
				<p class="text-xl max-w-3xl mx-auto">
					Here's a selection of projects I've developed, from interactive web apps to immersive 3D experiences.
					Each one reflects my passion for building creative, performant software using modern tools and thoughtful design.
					Each project represents a unique challenge and learning experience.
				</p>
			</div>
		</div>

		<!-- Featured Projects -->
		<div class="mb-16">
			<div class="bg-gray-700/40 backdrop-blur-sm rounded-lg p-6 mb-8">
				<h2 class="text-2xl font-bold text-white flex items-center">
					<span class="w-2 h-8 bg-blue-400 mr-4"></span>
					Featured Projects
				</h2>
			</div>

			<div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
				{#each projects.filter(p => p.featured) as project}
					<div class="bg-gray-800/60 backdrop-blur-sm rounded-lg overflow-hidden hover:bg-gray-700/60 transition-all duration-300 transform hover:scale-105">
						<div class="aspect-video bg-gray-800 overflow-hidden">
							<img
								src={project.image}
								alt={project.title}
								class="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
							/>
						</div>
						<div class="p-6">
							<h3 class="text-xl font-bold text-white mb-2">{project.title}</h3>
							<p class="text-gray-300 mb-4 text-sm">{project.description}</p>
							<div class="flex flex-wrap gap-2 mb-4">
								{#each project.technologies as tech}
									<span class="px-2 py-1 bg-blue-500/20 text-blue-300 text-xs rounded-full">
										{tech}
									</span>
								{/each}
							</div>
							<div class="flex gap-3">
								<a
									href={project.demoUrl}
									target="_blank"
									rel="noopener noreferrer"
									class="px-4 py-2 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"
								>
									Live Demo
								</a>
								<a
									href={project.githubUrl}
									target="_blank"
									rel="noopener noreferrer"
									class="px-4 py-2 border border-gray-400 text-gray-300 text-sm rounded hover:bg-gray-400 hover:text-black transition-colors"
								>
									GitHub
								</a>
							</div>
						</div>
					</div>
				{/each}
			</div>
		</div>

		<!-- All Projects - Horizontal Scroll -->
		<div class="mb-16">
			<div class="flex items-center justify-between mb-8">
				<div class="bg-gray-700/40 backdrop-blur-sm rounded-lg p-4">
					<h2 class="text-2xl font-bold text-white flex items-center">
						<span class="w-2 h-8 bg-purple-400 mr-4"></span>
						All Projects
					</h2>
				</div>
				<div class="flex gap-2">
					<button
						on:click={scrollLeft}
						class="p-2 bg-gray-800/60 text-white rounded-full hover:bg-gray-700/60 transition-colors"
						aria-label="Scroll left"
					>
						<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
						</svg>
					</button>
					<button
						on:click={scrollRight}
						class="p-2 bg-gray-800/60 text-white rounded-full hover:bg-gray-700/60 transition-colors"
						aria-label="Scroll right"
					>
						<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
						</svg>
					</button>
				</div>
			</div>

			<div
				bind:this={scrollContainer}
				class="flex gap-6 overflow-x-auto pb-4"
				style="scroll-snap-type: x mandatory;"
			>
				{#each projects as project}
					<div class="flex-none w-96 bg-gray-800/60 backdrop-blur-sm rounded-lg overflow-hidden hover:bg-gray-700/60 transition-all duration-300 transform hover:scale-105" style="scroll-snap-align: start;">
						<div class="aspect-video bg-gray-800 overflow-hidden">
							<img
								src={project.image}
								alt={project.title}
								class="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
							/>
						</div>
						<div class="p-6">
							<h3 class="text-xl font-bold text-white mb-2">{project.title}</h3>
							<p class="text-gray-300 mb-4 text-sm">{project.description}</p>
							<div class="flex flex-wrap gap-2 mb-4">
								{#each project.technologies as tech}
									<span class="px-2 py-1 bg-blue-500/20 text-blue-300 text-xs rounded-full">
										{tech}
									</span>
								{/each}
							</div>
							<div class="flex gap-3">
								<a
									href={project.demoUrl}
									target="_blank"
									rel="noopener noreferrer"
									class="px-4 py-2 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"
								>
									Live Demo
								</a>
								{#if project.githubUrl}
									<a
										href={project.githubUrl}
										target="_blank"
										rel="noopener noreferrer"
										class="px-4 py-2 border border-gray-400 text-gray-300 text-sm rounded hover:bg-gray-400 hover:text-black transition-colors"
									>
										GitHub
									</a>
								{/if}
							</div>
						</div>
					</div>
				{/each}
			</div>

			<!-- Horizontal Scroll Bar -->
			<div class="relative mt-4">
				<div class="bg-gray-700/40 backdrop-blur-sm rounded-lg p-2">
					<div class="bg-gray-600/60 h-2 rounded-full overflow-hidden">
						<div
							class="bg-gradient-to-r from-purple-400 to-blue-400 h-full rounded-full transition-all duration-300"
							style="width: 30%; transform: translateX(0%);"
							id="scroll-indicator"
						></div>
					</div>
				</div>
			</div>
		</div>

		<!-- Call to Action -->
		<div class="text-center">
			<div class="bg-gray-700/40 backdrop-blur-sm rounded-lg p-8 mb-8">
				<h3 class="text-2xl font-bold text-white mb-4">Interested in Working Together?</h3>
				<p class="text-gray-300 max-w-2xl mx-auto">
					I'm always excited to take on new challenges and collaborate on innovative projects.
					Let's discuss how we can bring your ideas to life.
				</p>
			</div>
			<a
				href="/contact"
				class="inline-block px-8 py-4 bg-gradient-to-r from-purple-500 to-blue-500 text-white font-semibold rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
			>
				Get In Touch
			</a>
		</div>
	</div>
</section>

<style>
	.scrollbar-hide {
		-ms-overflow-style: none;
		scrollbar-width: none;
	}

	.scrollbar-hide::-webkit-scrollbar {
		display: none;
	}

	.line-clamp-3 {
		display: -webkit-box;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
